import { requireAuth, getUserProfile } from '@/lib/auth'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { SalesTrackingDashboard } from '@/components/dashboard/sales-tracking/SalesTrackingDashboard'
import { redirect } from 'next/navigation'

export default async function SalesTrackingPage() {
  const user = await requireAuth()
  const profile = await getUserProfile()

  if (!profile) {
    redirect('/login')
  }

  // Check if user has permission to view sales tracking
  const allowedRoles = ['system_admin', 'area_manager', 'team_manager', 'sales_employee']
  if (!allowedRoles.includes(profile.role)) {
    redirect('/unauthorized')
  }

  return (
    <DashboardLayout user={{
      name: profile.full_name || user.email,
      email: user.email,
      role: profile.role
    }}>
      <SalesTrackingDashboard
        userRole={profile.role}
        userId={profile.id}
        userAreaId={profile.area_id}
        userTeamId={profile.team_id}
      />
    </DashboardLayout>
  )
}
